/**
 * Essential constants for interactive-agent
 * Minimal version based on interactive-mcp constants
 */

// User input timeout in seconds
export const USER_INPUT_TIMEOUT_SECONDS = 60 as const;

// Platform detection and spawning constants
export const TERMINAL_SPAWN_TIMEOUT_MS = 5000 as const;
export const PLATFORM_DETECTION_TIMEOUT_MS = 3000 as const;
export const MAX_SPAWN_RETRIES = 3 as const;
export const SPAWN_RETRY_DELAY_MS = 1000 as const;
export const PLATFORM_CACHE_TTL_MS = 300000 as const; // 5 minutes

// Shell paths for fallback detection
export const DEFAULT_SHELL_PATHS = {
  windows: ['cmd.exe', 'powershell.exe'],
  unix: ['/bin/bash', '/bin/sh', '/bin/zsh', '/bin/fish']
} as const;

// Terminal preferences by platform
export const TERMINAL_PREFERENCES = {
  win32: ['wt.exe', 'ConEmu64.exe', 'ConEmu.exe', 'cmd.exe'],
  darwin: ['Terminal.app', 'iTerm.app'],
  linux: ['gnome-terminal', 'konsole', 'xfce4-terminal', 'mate-terminal', 'xterm', 'urxvt']
} as const;

// Platform type definitions
export type PlatformType = 'win32' | 'darwin' | 'linux' | 'other';
