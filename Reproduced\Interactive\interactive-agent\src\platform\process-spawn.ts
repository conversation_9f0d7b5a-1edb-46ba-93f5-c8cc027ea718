import { spawn, ChildProcess, SpawnOptions as NodeSpawnOptions } from 'child_process';
import * as path from 'path';
import * as os from 'os';
import * as fs from 'fs';
import { getPlatformInfo, PlatformInfo } from './platform-detector.js';
import { TERMINAL_SPAWN_TIMEOUT_MS } from '../constants.js';

export interface SpawnOptions {
  cwd?: string;
  env?: Record<string, string>;
  detached?: boolean;
  windowTitle?: string;
  args?: string[];
}

export interface SpawnResult {
  process: ChildProcess | null;
  success: boolean;
  error?: string;
}

/**
 * Échappe les arguments pour la ligne de commande Windows
 */
function escapeWindowsArg(arg: string): string {
  if (!arg.includes(' ') && !arg.includes('"') && !arg.includes('\\')) {
    return arg;
  }
  
  return `"${arg.replace(/\\/g, '\\\\').replace(/"/g, '\\"')}"`;
}

/**
 * Échappe les arguments pour les shells Unix
 */
function escapeUnixArg(arg: string): string {
  if (!arg.includes(' ') && !arg.includes("'") && !arg.includes('"') && !arg.includes('\\')) {
    return arg;
  }

  return `'${arg.replace(/'/g, "'\"'\"'")}'`;
}

/**
 * Échappe une chaîne pour AppleScript
 * Gère tous les caractères spéciaux AppleScript de manière sécurisée
 */
function escapeAppleScript(str: string): string {
  return str
    // Échapper les backslashes en premier (important pour éviter les doubles échappements)
    .replace(/\\/g, '\\\\')
    // Échapper les guillemets doubles
    .replace(/"/g, '\\"')
    // Échapper les retours à la ligne
    .replace(/\n/g, '\\n')
    .replace(/\r/g, '\\r')
    // Échapper les tabulations
    .replace(/\t/g, '\\t')
    // Échapper les caractères de contrôle AppleScript et shell
    .replace(/\$/g, '\\$')
    .replace(/`/g, '\\`')
    // Échapper les accolades seulement dans le contexte des variables ${...}
    .replace(/\$\{/g, '\\$\\{')
    .replace(/\}/g, '\\}');
}

/**
 * Crée les options de spawn pour Windows
 */
function createWindowsSpawnOptions(
  command: string,
  args: string[] = [],
  options: SpawnOptions = {}
): { cmd: string; args: string[]; spawnOptions: NodeSpawnOptions } {
  const platformInfo = getPlatformInfo();
  
  // Utiliser Windows Terminal si disponible, sinon cmd.exe
  const useWindowsTerminal = platformInfo.availableTerminals.includes('wt.exe');
  
  if (useWindowsTerminal) {
    const escapedCommand = escapeWindowsArg(command);
    const escapedArgs = args.map(escapeWindowsArg).join(' ');
    const fullCommand = escapedArgs ? `${escapedCommand} ${escapedArgs}` : escapedCommand;
    
    const wtArgs = ['--title', options.windowTitle || 'Interactive Agent'];
    if (options.cwd) {
      wtArgs.push('--startingDirectory', options.cwd);
    }
    wtArgs.push('--', 'cmd.exe', '/k', fullCommand);
    
    return {
      cmd: 'wt.exe',
      args: wtArgs,
      spawnOptions: {
        detached: options.detached ?? true,
        stdio: 'ignore',
        env: { ...process.env, ...options.env },
        windowsHide: false
      }
    };
  } else {
    // Fallback vers cmd.exe
    const escapedCommand = escapeWindowsArg(command);
    const escapedArgs = args.map(escapeWindowsArg);
    
    return {
      cmd: 'cmd.exe',
      args: ['/k', escapedCommand, ...escapedArgs],
      spawnOptions: {
        detached: options.detached ?? true,
        stdio: 'ignore',
        env: { ...process.env, ...options.env },
        cwd: options.cwd,
        windowsHide: false
      }
    };
  }
}

/**
 * Crée les options de spawn pour macOS
 */
function createMacOSSpawnOptions(
  command: string,
  args: string[] = [],
  options: SpawnOptions = {}
): { cmd: string; args: string[]; spawnOptions: NodeSpawnOptions } {
  const escapedCommand = escapeUnixArg(command);
  const escapedArgs = args.map(escapeUnixArg).join(' ');
  const fullCommand = escapedArgs ? `${escapedCommand} ${escapedArgs}` : escapedCommand;

  // Approche sécurisée : utiliser un fichier de script temporaire
  // pour éviter les problèmes d'échappement complexes

  try {
    // Créer un fichier de script temporaire
    const tempDir = os.tmpdir();
    const scriptPath = path.join(tempDir, `terminal_script_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.sh`);

    // Écrire la commande dans le fichier temporaire
    fs.writeFileSync(scriptPath, `#!/bin/bash\n${fullCommand}\n`, { mode: 0o755 });

    // Utiliser osascript pour exécuter le script temporaire
    const appleScript = `
      tell application "Terminal"
        activate
        do script "bash '${scriptPath}'; rm '${scriptPath}'"
      end tell
    `;

    return {
      cmd: 'osascript',
      args: ['-e', appleScript],
      spawnOptions: {
        detached: options.detached ?? true,
        stdio: 'ignore',
        env: { ...process.env, ...options.env },
        cwd: options.cwd
      }
    };
  } catch (error) {
    // Fallback vers l'approche directe avec échappement amélioré
    console.warn('Impossible de créer un fichier temporaire, utilisation de l\'échappement direct:', error);

    // Utiliser la fonction d'échappement améliorée
    const escapedFullCommand = escapeAppleScript(fullCommand);

    const appleScript = `
      tell application "Terminal"
        activate
        do script "${escapedFullCommand}"
      end tell
    `;

    return {
      cmd: 'osascript',
      args: ['-e', appleScript],
      spawnOptions: {
        detached: options.detached ?? true,
        stdio: 'ignore',
        env: { ...process.env, ...options.env },
        cwd: options.cwd
      }
    };
  }
}

/**
 * Crée les options de spawn pour Linux
 */
function createLinuxSpawnOptions(
  command: string,
  args: string[] = [],
  options: SpawnOptions = {}
): { cmd: string; args: string[]; spawnOptions: NodeSpawnOptions } {
  const platformInfo = getPlatformInfo();
  const availableTerminals = platformInfo.availableTerminals;
  
  const escapedCommand = escapeUnixArg(command);
  const escapedArgs = args.map(escapeUnixArg).join(' ');
  const fullCommand = escapedArgs ? `${escapedCommand} ${escapedArgs}` : escapedCommand;
  
  // Essayer les terminaux dans l'ordre de préférence
  let terminalCmd = 'xterm';
  let terminalArgs: string[] = [];
  
  if (availableTerminals.includes('gnome-terminal')) {
    terminalCmd = 'gnome-terminal';
    terminalArgs = ['--', 'bash', '-c', fullCommand];
    if (options.windowTitle) {
      terminalArgs.unshift('--title', options.windowTitle);
    }
  } else if (availableTerminals.includes('konsole')) {
    terminalCmd = 'konsole';
    terminalArgs = ['-e', 'bash', '-c', fullCommand];
    if (options.windowTitle) {
      terminalArgs.unshift('--title', options.windowTitle);
    }
  } else if (availableTerminals.includes('xfce4-terminal')) {
    terminalCmd = 'xfce4-terminal';
    terminalArgs = ['-e', `bash -c "${fullCommand}"`];
    if (options.windowTitle) {
      terminalArgs.unshift('--title', options.windowTitle);
    }
  } else {
    // Fallback vers xterm
    terminalArgs = ['-e', 'bash', '-c', fullCommand];
    if (options.windowTitle) {
      terminalArgs.unshift('-title', options.windowTitle);
    }
  }
  
  return {
    cmd: terminalCmd,
    args: terminalArgs,
    spawnOptions: {
      detached: options.detached ?? true,
      stdio: 'ignore',
      env: { ...process.env, ...options.env },
      cwd: options.cwd
    }
  };
}

/**
 * Spawn un processus dans un nouveau terminal selon la plateforme
 */
export function spawnInTerminal(
  command: string,
  args: string[] = [],
  options: SpawnOptions = {}
): SpawnResult {
  const platformInfo = getPlatformInfo();
  
  // Vérifier si le spawn de terminal est possible
  if (!platformInfo.canSpawnTerminal) {
    return {
      process: null,
      success: false,
      error: 'Terminal spawning not available on this platform'
    };
  }
  
  try {
    let spawnConfig: { cmd: string; args: string[]; spawnOptions: NodeSpawnOptions };
    
    switch (platformInfo.platform) {
      case 'win32':
        spawnConfig = createWindowsSpawnOptions(command, args, options);
        break;
      case 'darwin':
        spawnConfig = createMacOSSpawnOptions(command, args, options);
        break;
      default: // Linux et autres Unix-like
        spawnConfig = createLinuxSpawnOptions(command, args, options);
        break;
    }
    
    const childProcess = spawn(spawnConfig.cmd, spawnConfig.args, spawnConfig.spawnOptions);
    
    // Gérer les erreurs de spawn
    childProcess.on('error', (error) => {
      console.error(`Erreur lors du spawn du terminal: ${error.message}`);
    });
    
    // Détacher le processus si demandé
    if (options.detached && childProcess.pid) {
      childProcess.unref();
    }
    
    return {
      process: childProcess,
      success: true
    };
    
  } catch (error) {
    return {
      process: null,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown spawn error'
    };
  }
}

/**
 * Teste si le spawn de terminal fonctionne sur cette plateforme
 */
export async function testTerminalSpawn(): Promise<boolean> {
  const result = spawnInTerminal('echo', ['test']);
  
  if (!result.success || !result.process) {
    return false;
  }
  
  return new Promise((resolve) => {
    const timeout = setTimeout(() => {
      resolve(false);
    }, TERMINAL_SPAWN_TIMEOUT_MS);
    
    result.process!.on('spawn', () => {
      clearTimeout(timeout);
      resolve(true);
    });
    
    result.process!.on('error', () => {
      clearTimeout(timeout);
      resolve(false);
    });
  });
}
